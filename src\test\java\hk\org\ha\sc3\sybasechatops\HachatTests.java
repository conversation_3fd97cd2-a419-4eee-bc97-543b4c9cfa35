package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.config.HachatConfig;
import hk.org.ha.sc3.sybasechatops.model.db.ChatopsUser;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatCreateRoomReturn;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatGetAccInfoReturn;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatLoginReturn;
import hk.org.ha.sc3.sybasechatops.repository.ChatopsUserRepository;
import hk.org.ha.sc3.sybasechatops.repository.ChatroomRepository;
import hk.org.ha.sc3.sybasechatops.service.HachatService;
import lombok.extern.slf4j.Slf4j;
import reactor.netty.http.client.HttpClient;

@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
public class HachatTests {

    @MockBean
    private ChatopsUserRepository chatopsUserRepository;

    @MockBean
    private ChatroomRepository chatroomRepository;

    @Autowired
    private HachatConfig hachatConfig;

    @Autowired
    private HttpClient sslHttpClient;

    private HachatService hachatService;

    // Shared API token for all test cases
    private static String apiToken;
    private static final String TEST_USERNAME = "sc3automationsupport";

    @BeforeEach
    void setUp() {
        // Initialize HachatService with mocked repositories
        hachatService = new HachatService(hachatConfig, sslHttpClient, chatopsUserRepository, chatroomRepository);

        // Set up mock data for repositories
        setupMockData();

        // Set up API token if not already done
        if (apiToken == null) {
            setupApiToken();
        }
    }

    private void setupApiToken() {
        log.info("Setting up API token for HachatTests");

        // Note: This will still use real Hachat API for login, but repositories are mocked
        HachatLoginReturn loginResult = hachatService.hachatLogin()
                .block();

        assertNotNull(loginResult, "Login result should not be null");
        assertEquals("success", loginResult.getStatus(), "Login status should be 'success'");
        assertNotNull(loginResult.getApiKey(), "API key should not be null after successful login");

        apiToken = loginResult.getApiKey();
        log.info("API token obtained successfully for subsequent tests");
    }

    private void setupMockData() {
        // Mock ChatopsUserRepository data
        ChatopsUser mockUser1 = new ChatopsUser();
        mockUser1.setCorpId("ccy420a");
        mockUser1.setTeam("SC11");
        mockUser1.setTeamId(null);

        ChatopsUser mockUser2 = new ChatopsUser();
        mockUser2.setCorpId("lkp625");
        mockUser2.setTeam("SC11");
        mockUser2.setTeamId(null);

        List<ChatopsUser> mockUsers = Arrays.asList(mockUser1, mockUser2);

        when(chatopsUserRepository.findAll()).thenReturn(mockUsers);
        when(chatopsUserRepository.findTeamsByCorpId("ccy420a")).thenReturn(Arrays.asList("SC11"));
        when(chatopsUserRepository.findTeamsByCorpId("lkp625")).thenReturn(Arrays.asList("SC11"));

        // Mock ChatroomRepository data
        when(chatroomRepository.findChatroomsByTeam("SC11")).thenReturn(Arrays.asList("79487","79545"));
        when(chatroomRepository.findRoomsWithoutId()).thenReturn(Arrays.asList("Test Room null id 1", "Test Room null id 2"));
        when(chatroomRepository.updateRoomId(anyString(), anyString())).thenReturn(1);
    }


    @Test
    void testFindAllUsers() {
        // This test verifies that the addUserToAllChatrooms method can access all users
        List<ChatopsUser> allUsers = chatopsUserRepository.findAll();

        // Verify we get the mocked data
        assertNotNull(allUsers, "Users list should not be null");
        assertEquals(2, allUsers.size(), "Should return 2 users");

        // Verify first user
        ChatopsUser user1 = allUsers.get(0);
        assertEquals("ccy420a", user1.getCorpId(), "First user corpId should be ccy420a");
        assertEquals("SC11", user1.getTeam(), "First user team should be SC11");

        // Verify second user
        ChatopsUser user2 = allUsers.get(1);
        assertEquals("lkp625", user2.getCorpId(), "Second user corpId should be lkp625");
        assertEquals("SC11", user2.getTeam(), "Second user team should be SC11");

        log.info("Mocked users: {}", allUsers.stream().map(ChatopsUser::getCorpId).collect(java.util.stream.Collectors.toList()));
    }

    @Test
    void testGetChatroomIdByTeam() {
       List<String> chatrooms = hachatService.getChatroomIdByTeam("SC11");

       // Verify we get the mocked data
       assertNotNull(chatrooms, "Chatrooms list should not be null");
       assertEquals(2, chatrooms.size(), "Should return 2 chatrooms for SC11");
       assertEquals("79487", chatrooms.get(0), "First chatroom should be 79487");
       assertEquals("79545", chatrooms.get(1), "Second chatroom should be 79545");

       log.info("Mocked chatrooms for SC11: {}", chatrooms);
    }

     @Test
     void testGetTeamWithCorpId() {
        List<String> teams = hachatService.getTeamsByCorpId("ccy420a");

        // Verify we get the mocked data
        assertNotNull(teams, "Teams list should not be null");
        assertEquals(1, teams.size(), "Should return 1 team for ccy420a");
        assertEquals("SC11", teams.get(0), "Team should be SC11");

        log.info("Mocked teams for ccy420a: {}", teams);
     }

     @Test
     void testGetTeamWithCorpIdSecondUser() {
        List<String> teams = hachatService.getTeamsByCorpId("lkp625");

        // Verify we get the mocked data
        assertNotNull(teams, "Teams list should not be null");
        assertEquals(1, teams.size(), "Should return 1 team for lkp625");
        assertEquals("SC11", teams.get(0), "Team should be SC11");

        log.info("Mocked teams for lkp625: {}", teams);
     }

     @Test
     void testGetRoomWithNullId() {
        List<String> rooms = hachatService.getRoomListWithNullId();

        // Verify we get the mocked data
        assertNotNull(rooms, "Rooms list should not be null");
        assertEquals(2, rooms.size(), "Should return 2 rooms without ID");
        assertEquals("Test Room null id 1", rooms.get(0), "First room should be 'Test Room null id 1'");
        assertEquals("Test Room null id 2", rooms.get(1), "Second room should be 'Test Room null id 2'");

        log.info("Mocked rooms without ID: {}", rooms);
     }

    @Test
    void testHachatGetAccInfo() {
        assertNotNull(apiToken, "API token should be available from setup");
        
        HachatGetAccInfoReturn HachatGetAccInfoReturn = hachatService.getAccInfo(apiToken, "lkp625")
                .block();
        log.info("Account id for {}: {}", "lkp625", HachatGetAccInfoReturn.getAccountId());
        assertNotNull(HachatGetAccInfoReturn, "Return should not be null");
    }

    @Test
    void testHachatViewRoom() {
        assertNotNull(apiToken, "API token should be available from setup");
        
        String result = hachatService.viewRoom(apiToken, 42391)
                .block();
        log.info(result);
        assertNotNull(result, "Return should not be null");
    }

    @Test
    void testHachatReceiveMsg() {
        assertNotNull(apiToken, "API token should be available from setup");
        
        String result = hachatService.receiveMsg(apiToken, 5, 42391, "up")
                .block();
        assertNotNull(result, "Return should not be null");
    }

    @Test
    void testHachatCreateRoom() {
        assertNotNull(apiToken, "API token should be available from setup");
        int[] memberIds = {102663, 103695};
        HachatCreateRoomReturn HachatCreateRoomReturn = hachatService.createRoom(apiToken, memberIds, "Create Room")
                .block();
        assertNotNull(HachatCreateRoomReturn, "Return should not be null");
    }


    @Test
    void testHachatAddRoomMember() {
        assertNotNull(apiToken, "API token should be available from setup");
        
        String result = hachatService.addRoomMembers(apiToken, 103695, 79477)
                .block();
        assertNotNull(result, "Return should not be null");
    }

    @Test
    void testUpdateRoomId() {
        String result = hachatService.updateRoomId("DB Chatops (SC11)", "12345")
                .block();

        // Verify the mock returned the expected result
        assertNotNull(result, "Result should not be null");
        assertEquals("Updated", result, "Should return 'Updated' when mock returns 1 row affected");

        log.info("Mocked updateRoomId result: {}", result);
    }

    @Test
    void testCreateAndUpdateRoom() {
        int[] memberIds = {102663, 103695};
        String result = hachatService.createAndUpdateRoom(apiToken, memberIds)
                .blockLast();
    
        System.out.println("Result: " + result);
        
}

    @Test
    void testAddUserToAllChatrooms() {
        String result = hachatService.addUserToAllChatrooms(apiToken)
                .blockLast();
    
        System.out.println("Result: " + result);
        
}

    @Test
    void testHachatLogout() {
        assertNotNull(apiToken, "API token should be available from setup");
        
        String result = hachatService.hachatLogout(apiToken, TEST_USERNAME)
                .block();
        assertNotNull(result, "Return should not be null");
    }
}
